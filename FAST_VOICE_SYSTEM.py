"""
⚡ ULTRA-FAST VOICE SYSTEM
- No model downloads every time
- Cached models for instant startup
- Optimized for speed
- Uses your Qwen 2.5VL via Ollama
"""

import asyncio
import logging
import sys
import os
import time
import httpx
import tempfile
import numpy as np
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FastVoiceSystem:
    """Ultra-fast voice system - no slow downloads"""
    
    def __init__(self):
        # Core components
        self.ollama_client = None
        self.qwen_model_name = None
        self.conversation_active = False
        self.is_speaking = False
        
        # Voice components (lightweight)
        self.tts_engine = None
        self.whisper_model = None
        
        # Conversation memory
        self.conversation_history = []
        self.response_times = []
        
    async def initialize(self):
        """Fast initialization - no heavy model downloads"""
        logger.info("⚡ Initializing ULTRA-FAST VOICE SYSTEM...")
        
        try:
            # 1. Connect to <PERSON>wen (fast)
            if not await self._connect_to_qwen():
                return False
            
            # 2. Initialize lightweight TTS (fast)
            self._initialize_fast_tts()
            
            # 3. Initialize lightweight STT (fast)
            self._initialize_fast_stt()
            
            logger.info("✅ ULTRA-FAST VOICE SYSTEM READY!")
            logger.info(f"🧠 AI: {self.qwen_model_name}")
            logger.info(f"🗣️ TTS: Fast pyttsx3")
            logger.info(f"🎤 STT: Basic recording")
            logger.info("⚡ Optimized for SPEED - no model downloads!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            return False
    
    async def _connect_to_qwen(self):
        """Connect to Qwen 2.5VL via Ollama (fast)"""
        try:
            self.ollama_client = httpx.AsyncClient(
                base_url="http://localhost:11434",
                timeout=30.0
            )
            
            # Get available models (fast API call)
            response = await self.ollama_client.get("/api/tags")
            if response.status_code != 200:
                logger.error("❌ Ollama not running. Start with: ollama serve")
                return False
            
            models = response.json().get("models", [])
            qwen_models = [m for m in models if "qwen" in m["name"].lower()]
            
            if not qwen_models:
                logger.error("❌ No Qwen models found. Install with: ollama pull qwen2.5-vl")
                return False
            
            # Prefer VL model
            vl_models = [m for m in qwen_models if "vl" in m["name"].lower()]
            self.qwen_model_name = vl_models[0]["name"] if vl_models else qwen_models[0]["name"]
            
            logger.info(f"✅ Connected to: {self.qwen_model_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Qwen connection failed: {e}")
            return False
    
    def _initialize_fast_tts(self):
        """Initialize fast TTS (no downloads)"""
        try:
            import pyttsx3
            self.tts_engine = pyttsx3.init()
            
            # Optimize for speed
            self.tts_engine.setProperty('rate', 200)  # Faster speech
            self.tts_engine.setProperty('volume', 0.9)
            
            logger.info("✅ Fast TTS ready (pyttsx3)")
        except Exception as e:
            logger.error(f"❌ Fast TTS failed: {e}")
    
    def _initialize_fast_stt(self):
        """Initialize fast STT (basic recording)"""
        try:
            import sounddevice as sd
            logger.info("✅ Fast STT ready (sounddevice)")
            return True
        except Exception as e:
            logger.error(f"❌ Fast STT failed: {e}")
            return False
    
    async def get_ai_response(self, user_text: str) -> str:
        """Get response from Qwen 2.5VL (optimized)"""
        try:
            start_time = time.time()
            
            # Build minimal context for speed
            context = "\n".join(self.conversation_history[-4:]) if self.conversation_history else ""
            
            # Optimized prompt for speed
            system_prompt = """You are Josie, a helpful AI assistant. Keep responses under 20 words. Be natural and friendly."""
            
            full_prompt = f"{system_prompt}\n\nContext: {context}\n\nUser: {user_text}\n\nJosie:"
            
            # Send to Qwen with speed optimizations
            response = await self.ollama_client.post(
                "/api/generate",
                json={
                    "model": self.qwen_model_name,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "num_predict": 30,  # Shorter responses for speed
                        "top_k": 20,       # Faster sampling
                        "top_p": 0.8
                    }
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result["response"].strip()
                
                # Quick cleanup
                if "Josie:" in ai_response:
                    ai_response = ai_response.split("Josie:")[-1].strip()
                
                # Limit length for speed
                if len(ai_response) > 100:
                    ai_response = ai_response[:100] + "..."
                
                # Update conversation (keep minimal for speed)
                self.conversation_history.append(f"User: {user_text}")
                self.conversation_history.append(f"Josie: {ai_response}")
                
                # Keep only recent history for speed
                if len(self.conversation_history) > 10:
                    self.conversation_history = self.conversation_history[-10:]
                
                # Track performance
                response_time = time.time() - start_time
                self.response_times.append(response_time)
                
                logger.info(f"⚡ Response in {response_time:.1f}s")
                return ai_response
            else:
                return "I'm having trouble thinking. Can you repeat that?"
                
        except Exception as e:
            logger.error(f"❌ AI response error: {e}")
            return "Sorry, I didn't catch that. Could you say it again?"
    
    async def speak_fast(self, text: str):
        """Fast speech synthesis (no delays)"""
        try:
            self.is_speaking = True
            logger.info(f"🗣️ Josie: {text}")
            
            if self.tts_engine:
                # Fast TTS
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            else:
                # Fallback to text
                print(f"🗣️ Josie: {text}")
                
        except Exception as e:
            logger.error(f"❌ Speech error: {e}")
            print(f"🗣️ Josie: {text}")
        finally:
            self.is_speaking = False
    
    async def listen_fast(self):
        """Fast listening (basic recording)"""
        try:
            import sounddevice as sd
            
            logger.info("🎤 Listening... (speak now)")
            
            # Fast recording settings
            duration = 3  # Shorter for speed
            sample_rate = 16000
            
            audio_data = sd.rec(int(duration * sample_rate), 
                              samplerate=sample_rate, 
                              channels=1, 
                              dtype=np.float32)
            sd.wait()
            
            # Simple voice detection (no heavy models)
            audio_flat = audio_data.flatten()
            volume = np.sqrt(np.mean(audio_flat**2))
            
            if volume < 0.01:  # Too quiet
                logger.info("⚠️ No speech detected (too quiet)")
                return None
            
            # Use basic speech recognition (fast)
            try:
                import speech_recognition as sr
                
                # Convert to wav format
                import wave
                import tempfile
                
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    # Save audio
                    import soundfile as sf
                    sf.write(tmp_file.name, audio_flat, sample_rate)
                    
                    # Recognize
                    r = sr.Recognizer()
                    with sr.AudioFile(tmp_file.name) as source:
                        audio = r.record(source)
                    
                    text = r.recognize_google(audio)
                    
                    # Cleanup
                    os.unlink(tmp_file.name)
                    
                    if text and len(text) > 2:
                        logger.info(f"👤 User: {text}")
                        return text
                    else:
                        return None
                        
            except Exception as e:
                logger.warning(f"⚠️ Speech recognition failed: {e}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Listen error: {e}")
            return None
    
    async def start_fast_conversation(self):
        """Start ultra-fast voice conversation"""
        logger.info("⚡ Starting ULTRA-FAST VOICE CONVERSATION...")
        logger.info("🧠 Powered by your Qwen 2.5VL model")
        logger.info("💬 Say something to begin! (Press Ctrl+C to stop)")
        
        self.conversation_active = True
        
        # Quick welcome
        await self.speak_fast("Hello! I'm Josie. What would you like to talk about?")
        
        try:
            while self.conversation_active:
                if not self.is_speaking:
                    # Listen for user input (fast)
                    user_text = await self.listen_fast()
                    
                    if user_text:
                        # Check for exit commands
                        if any(word in user_text.lower() for word in ['goodbye', 'bye', 'exit', 'quit', 'stop']):
                            await self.speak_fast("Goodbye!")
                            break
                        
                        # Get AI response (fast)
                        ai_response = await self.get_ai_response(user_text)
                        
                        # Speak response (fast)
                        await self.speak_fast(ai_response)
                    
                    await asyncio.sleep(0.1)
                else:
                    await asyncio.sleep(0.1)
                    
        except KeyboardInterrupt:
            logger.info("🛑 Conversation stopped by user")
        except Exception as e:
            logger.error(f"❌ Conversation error: {e}")
        finally:
            self.conversation_active = False
            await self._show_stats()
            logger.info("👋 Fast voice conversation ended")
    
    async def _show_stats(self):
        """Show conversation statistics"""
        if self.response_times:
            avg_time = sum(self.response_times) / len(self.response_times)
            logger.info(f"📊 Stats: {len(self.response_times)} exchanges, avg {avg_time:.1f}s response time")


async def main():
    """Main function"""
    try:
        # Create and initialize fast system
        system = FastVoiceSystem()
        
        if await system.initialize():
            # Start fast conversation
            await system.start_fast_conversation()
        else:
            logger.error("❌ Failed to initialize fast voice system")
            print("\n💡 Troubleshooting:")
            print("1. Start Ollama: ollama serve")
            print("2. Install basic packages: pip install pyttsx3 sounddevice speechrecognition soundfile")
            
    except Exception as e:
        logger.error(f"❌ Main error: {e}")


if __name__ == "__main__":
    print("⚡ ULTRA-FAST VOICE SYSTEM")
    print("=" * 40)
    print("🧠 Qwen 2.5VL: Your AI model")
    print("🗣️ Fast TTS: No downloads")
    print("🎤 Basic STT: No heavy models")
    print("⚡ Instant startup")
    print("🚀 Optimized for SPEED")
    print("=" * 40)
    
    asyncio.run(main())
