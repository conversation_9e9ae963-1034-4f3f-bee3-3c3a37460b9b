"""
🚀 CLEAN VOICE SYSTEM INSTALLER
Installs all dependencies for the ultimate voice system
"""

import subprocess
import sys
import logging
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class CleanInstaller:
    """Clean installer for voice system dependencies"""
    
    def __init__(self):
        self.installed_packages = []
        self.failed_packages = []
        
    def install_package(self, package_name, description=""):
        """Install a single package"""
        try:
            logger.info(f"📦 Installing {package_name}... {description}")
            
            # Use pip to install
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package_name],
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {package_name} installed successfully")
                self.installed_packages.append(package_name)
                return True
            else:
                logger.error(f"❌ {package_name} failed: {result.stderr}")
                self.failed_packages.append(package_name)
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {package_name} timed out")
            self.failed_packages.append(package_name)
            return False
        except Exception as e:
            logger.error(f"❌ {package_name} error: {e}")
            self.failed_packages.append(package_name)
            return False
    
    def install_git_package(self, git_url, package_name):
        """Install package from git repository"""
        try:
            logger.info(f"📦 Installing {package_name} from git...")
            
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", f"git+{git_url}"],
                capture_output=True,
                text=True,
                timeout=600
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {package_name} (git) installed successfully")
                self.installed_packages.append(package_name)
                return True
            else:
                logger.error(f"❌ {package_name} (git) failed: {result.stderr}")
                self.failed_packages.append(package_name)
                return False
                
        except Exception as e:
            logger.error(f"❌ {package_name} (git) error: {e}")
            self.failed_packages.append(package_name)
            return False
    
    def install_all_dependencies(self):
        """Install all required dependencies"""
        logger.info("🚀 Starting CLEAN VOICE SYSTEM installation...")
        
        # Core packages
        core_packages = [
            ("torch", "PyTorch for AI models"),
            ("torchaudio", "Audio processing"),
            ("numpy", "Numerical computing"),
            ("scipy", "Scientific computing"),
            ("soundfile", "Audio file handling"),
            ("sounddevice", "Audio recording/playback"),
            ("pygame", "Audio playback"),
        ]
        
        # AI/ML packages
        ai_packages = [
            ("transformers", "Hugging Face transformers"),
            ("accelerate", "Model acceleration"),
            ("openai-whisper", "Speech recognition"),
            ("httpx", "HTTP client for Ollama"),
        ]
        
        # TTS packages
        tts_packages = [
            ("pyttsx3", "Text-to-speech fallback"),
            ("edge-tts", "Microsoft Edge TTS"),
        ]
        
        # Advanced packages
        advanced_packages = [
            ("silero-vad", "Advanced voice detection"),
        ]
        
        # Install core packages
        logger.info("📦 Installing CORE packages...")
        for package, desc in core_packages:
            self.install_package(package, desc)
        
        # Install AI packages
        logger.info("📦 Installing AI/ML packages...")
        for package, desc in ai_packages:
            self.install_package(package, desc)
        
        # Install TTS packages
        logger.info("📦 Installing TTS packages...")
        for package, desc in tts_packages:
            self.install_package(package, desc)
        
        # Install advanced packages
        logger.info("📦 Installing ADVANCED packages...")
        for package, desc in advanced_packages:
            self.install_package(package, desc)
        
        # Install premium TTS from git
        logger.info("📦 Installing PREMIUM TTS packages...")
        
        # ChatTTS
        self.install_git_package(
            "https://github.com/2noise/ChatTTS.git",
            "ChatTTS"
        )
        
        # Show results
        self.show_installation_results()
    
    def show_installation_results(self):
        """Show installation results"""
        total = len(self.installed_packages) + len(self.failed_packages)
        success_rate = (len(self.installed_packages) / total * 100) if total > 0 else 0
        
        logger.info("=" * 60)
        logger.info("📊 INSTALLATION RESULTS")
        logger.info("=" * 60)
        logger.info(f"✅ Successful: {len(self.installed_packages)}")
        logger.info(f"❌ Failed: {len(self.failed_packages)}")
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.installed_packages:
            logger.info("\n✅ SUCCESSFULLY INSTALLED:")
            for package in self.installed_packages:
                logger.info(f"  • {package}")
        
        if self.failed_packages:
            logger.info("\n❌ FAILED TO INSTALL:")
            for package in self.failed_packages:
                logger.info(f"  • {package}")
            
            logger.info("\n💡 MANUAL INSTALLATION:")
            logger.info("For failed packages, try:")
            for package in self.failed_packages:
                logger.info(f"  pip install {package}")
        
        logger.info("=" * 60)
        
        if success_rate >= 80:
            logger.info("🎉 Installation mostly successful! Ready to run voice system.")
        elif success_rate >= 60:
            logger.info("⚠️ Partial installation. Some features may not work.")
        else:
            logger.info("❌ Installation had issues. Check failed packages.")
    
    def test_installations(self):
        """Test key installations"""
        logger.info("🧪 Testing key installations...")
        
        tests = [
            ("torch", "import torch; print(f'PyTorch: {torch.__version__}')"),
            ("whisper", "import whisper; print('Whisper: OK')"),
            ("httpx", "import httpx; print('HTTPX: OK')"),
            ("sounddevice", "import sounddevice; print('SoundDevice: OK')"),
            ("pyttsx3", "import pyttsx3; print('pyttsx3: OK')"),
        ]
        
        for package, test_code in tests:
            try:
                result = subprocess.run(
                    [sys.executable, "-c", test_code],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    logger.info(f"✅ {package}: {result.stdout.strip()}")
                else:
                    logger.error(f"❌ {package}: Failed test")
                    
            except Exception as e:
                logger.error(f"❌ {package}: Test error - {e}")


def main():
    """Main installation function"""
    print("🚀 CLEAN VOICE SYSTEM INSTALLER")
    print("=" * 50)
    print("Installing all dependencies for ultimate voice system...")
    print("This may take several minutes...")
    print("=" * 50)
    
    try:
        installer = CleanInstaller()
        
        # Install all dependencies
        installer.install_all_dependencies()
        
        # Test installations
        installer.test_installations()
        
        print("\n🎯 NEXT STEPS:")
        print("1. Make sure Ollama is running: ollama serve")
        print("2. Install Qwen 2.5VL: ollama pull qwen2.5-vl")
        print("3. Run the voice system: python CLEAN_VOICE_SYSTEM.py")
        
    except KeyboardInterrupt:
        logger.info("🛑 Installation cancelled by user")
    except Exception as e:
        logger.error(f"❌ Installation error: {e}")


if __name__ == "__main__":
    main()
