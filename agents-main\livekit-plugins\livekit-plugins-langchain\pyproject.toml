[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "livekit-plugins-langchain"
dynamic = ["version"]
description = "LangChain/LangGraph plugin for LiveKit agents"
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.9.0"
authors = [
    {name = "LiveKit", email = "<EMAIL>"}
]
keywords = ["livekit", "agents", "LangChain", "LangGraph"]
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3 :: Only",
]
dependencies = [
    "livekit-agents>=1.1.4",
    "langchain-core>=0.3.0",
    "langgraph>=0.3.0",
]

[project.urls]
Documentation = "https://docs.livekit.io"
Website = "https://livekit.io/"
Source = "https://github.com/livekit/agents"

[tool.hatch.version]
path = "livekit/plugins/langchain/version.py"

[tool.hatch.build.targets.wheel]
packages = ["livekit"]

[tool.hatch.build.targets.sdist]
include = ["/livekit"]
