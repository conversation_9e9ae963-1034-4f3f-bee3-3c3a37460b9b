[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "livekit-plugins-hume"
dynamic = ["version"]
description = "Hume TTS plugin for LiveKit agents"
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.9.0"
authors = [
    {name = "LiveKit", email = "<EMAIL>"}
]
keywords = ["webrtc", "realtime", "audio", "livekit", "HumeAI", "Hume", "Octave"]
classifiers = [
    "Intended Audience :: Developers",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3 :: Only",
]
dependencies = [
    "aiohttp>=3.8.0",
    "livekit-agents>=1.1.4",
]

[project.urls]
Documentation = "https://docs.livekit.io"
Website = "https://livekit.io/"
Source = "https://github.com/livekit/agents"

[tool.hatch.version]
path = "livekit/plugins/hume/version.py"

[tool.hatch.build.targets.wheel]
packages = ["livekit"]

[tool.hatch.build.targets.sdist]
include = ["/livekit"]