# AWS plugin for LiveKit Agents

Support for AWS AI including <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.

See [https://docs.livekit.io/agents/integrations/aws/](https://docs.livekit.io/agents/integrations/aws/) for more information.

## Installation

```bash
pip install livekit-plugins-aws
```

## Pre-requisites

You'll need to specify an AWS Access Key and a Deployment Region. They can be set as environment variables: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY` and `AWS_DEFAULT_REGION`, respectively.