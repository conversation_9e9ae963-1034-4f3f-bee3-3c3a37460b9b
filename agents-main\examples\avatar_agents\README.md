# Avatar Examples

Avatars provide a visual representation for agents.


+ **audio_wave:** A simple local mock demo that visualizes audio input as waveforms. It demonstrates how to build a avatar service.

+ **bey:** The bey example shows how to use the [Beyond Presence API](https://docs.bey.dev/introduction) for avatar generation.


## Contributing

Feel free to contribute additional avatar examples or technical improvements to existing ones by submitting a pull request.
