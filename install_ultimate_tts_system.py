"""
ULTIMATE TTS AND VAD SYSTEM INSTALLER
Install the best local TTS models and advanced VAD for ultra-natural voice conversation
"""

import subprocess
import sys
import logging
import os
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(command, description):
    """Run a command and log the result"""
    logger.info(f"🔧 {description}...")
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=600  # 10 minutes timeout
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {description} - SUCCESS")
            return True
        else:
            logger.error(f"❌ {description} - FAILED")
            logger.error(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        logger.error(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        logger.error(f"💥 {description} - EXCEPTION: {e}")
        return False


def install_ultimate_tts_system():
    """Install the ultimate TTS and VAD system"""
    logger.info("🚀 Installing ULTIMATE TTS AND VAD SYSTEM...")
    
    # Track installation results
    results = {}
    
    # 1. Install F5-TTS (Best-in-class local TTS)
    logger.info("\n📦 INSTALLING F5-TTS (ULTIMATE LOCAL TTS)...")
    
    # Install PyTorch first (if not already installed)
    results['pytorch'] = run_command(
        "python -m pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu124",
        "Installing PyTorch with CUDA support"
    )
    
    # Install F5-TTS
    results['f5_tts'] = run_command(
        "python -m pip install f5-tts",
        "Installing F5-TTS (Ultimate local TTS)"
    )
    
    # 2. Install ChatTTS (Conversational TTS backup)
    logger.info("\n📦 INSTALLING CHATTTS (CONVERSATIONAL TTS)...")
    results['chattts'] = run_command(
        "python -m pip install chattts",
        "Installing ChatTTS (Conversational TTS)"
    )
    
    # 3. Install Advanced VAD Systems
    logger.info("\n📦 INSTALLING ADVANCED VAD SYSTEMS...")
    
    # Silero VAD (State-of-the-art)
    results['silero_vad'] = run_command(
        "python -m pip install silero-vad",
        "Installing Silero VAD (Advanced voice activity detection)"
    )
    
    # PyAnnote Audio (Professional VAD)
    results['pyannote'] = run_command(
        "python -m pip install pyannote.audio",
        "Installing PyAnnote Audio (Professional VAD)"
    )
    
    # 4. Install Additional Audio Processing Libraries
    logger.info("\n📦 INSTALLING AUDIO PROCESSING LIBRARIES...")
    
    # Advanced audio processing
    audio_packages = [
        "librosa",
        "soundfile", 
        "resampy",
        "noisereduce",
        "scipy",
        "numpy",
        "torchaudio",
        "transformers",
        "accelerate",
        "datasets"
    ]
    
    for package in audio_packages:
        results[package] = run_command(
            f"python -m pip install {package}",
            f"Installing {package}"
        )
    
    # 5. Install Gradio for web interface
    results['gradio'] = run_command(
        "python -m pip install gradio",
        "Installing Gradio for web interface"
    )
    
    # 6. Install additional TTS models
    logger.info("\n📦 INSTALLING ADDITIONAL TTS MODELS...")
    
    # Coqui TTS (if compatible)
    results['coqui_tts'] = run_command(
        "python -m pip install TTS",
        "Installing Coqui TTS (Additional TTS option)"
    )
    
    # StyleTTS2 dependencies
    results['styletts2_deps'] = run_command(
        "python -m pip install phonemizer espeak-ng",
        "Installing StyleTTS2 dependencies"
    )
    
    # 7. Download F5-TTS models
    logger.info("\n📦 DOWNLOADING F5-TTS MODELS...")
    
    # Create models directory
    models_dir = Path("models/f5_tts")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # Download base model (this will be done automatically on first use)
    logger.info("✅ F5-TTS models will be downloaded automatically on first use")
    
    # Print installation summary
    logger.info("\n" + "="*80)
    logger.info("🎉 ULTIMATE TTS AND VAD SYSTEM INSTALLATION SUMMARY")
    logger.info("="*80)
    
    successful = 0
    failed = 0
    
    for component, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"{component:25} : {status}")
        if success:
            successful += 1
        else:
            failed += 1
    
    logger.info(f"\n📊 INSTALLATION RESULTS:")
    logger.info(f"   ✅ Successful: {successful}")
    logger.info(f"   ❌ Failed: {failed}")
    logger.info(f"   📈 Success Rate: {successful/(successful+failed)*100:.1f}%")
    
    if successful > failed:
        logger.info("\n🎉 ULTIMATE TTS SYSTEM INSTALLATION COMPLETED SUCCESSFULLY!")
        logger.info("🚀 Ready to deploy ultra-natural voice conversation!")
    else:
        logger.warning("\n⚠️ Some components failed to install. System may still work with available components.")
    
    return results


def test_installations():
    """Test the installed TTS and VAD systems"""
    logger.info("\n🧪 TESTING INSTALLED SYSTEMS...")
    
    # Test F5-TTS
    try:
        import f5_tts
        logger.info("✅ F5-TTS import successful")
    except ImportError as e:
        logger.error(f"❌ F5-TTS import failed: {e}")
    
    # Test ChatTTS
    try:
        import ChatTTS
        logger.info("✅ ChatTTS import successful")
    except ImportError as e:
        logger.error(f"❌ ChatTTS import failed: {e}")
    
    # Test Silero VAD
    try:
        import silero_vad
        logger.info("✅ Silero VAD import successful")
    except ImportError as e:
        logger.error(f"❌ Silero VAD import failed: {e}")
    
    # Test PyAnnote
    try:
        import pyannote.audio
        logger.info("✅ PyAnnote Audio import successful")
    except ImportError as e:
        logger.error(f"❌ PyAnnote Audio import failed: {e}")
    
    # Test audio libraries
    try:
        import librosa
        import soundfile
        import torch
        import torchaudio
        logger.info("✅ Audio processing libraries import successful")
    except ImportError as e:
        logger.error(f"❌ Audio libraries import failed: {e}")


if __name__ == "__main__":
    logger.info("🎭 ULTIMATE TTS AND VAD SYSTEM INSTALLER")
    logger.info("=" * 60)
    
    # Install the ultimate system
    results = install_ultimate_tts_system()
    
    # Test installations
    test_installations()
    
    logger.info("\n🎯 NEXT STEPS:")
    logger.info("1. Run the new ultra_natural_voice_chat.py for ultimate voice quality")
    logger.info("2. Experience ElevenLabs-level voice synthesis locally")
    logger.info("3. Enjoy advanced VAD for natural conversation flow")
    logger.info("4. All processing remains 100% local and private")
    
    logger.info("\n🚀 ULTIMATE VOICE SYSTEM READY FOR DEPLOYMENT!")
