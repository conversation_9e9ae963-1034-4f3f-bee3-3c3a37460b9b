"""
Ollama LLM integration for local AI models
Provides interface to local Ollama models including Qwen 2.5-VL
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List, Optional, Union

import httpx
from livekit.agents import llm
from livekit.plugins.openai import LLM as OpenAILLM

logger = logging.getLogger(__name__)


class OllamaLLM(llm.LLM):
    """Ollama LLM implementation for local models"""
    
    def __init__(
        self,
        *,
        model: str = "goekden<PERSON><PERSON><PERSON><PERSON>/JOSIEFIED-Qwen3:14b",
        base_url: str = "http://localhost:11434/v1",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        timeout: float = 60.0,
        system_prompt: Optional[str] = None,
        clean_response: bool = True,
    ):
        """
        Initialize Ollama LLM
        
        Args:
            model: Ollama model name
            base_url: Ollama server URL
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling parameter
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            timeout: Request timeout in seconds
            system_prompt: System prompt for the model
            clean_response: Whether to clean model responses (remove thinking tags)
        """
        super().__init__()
        
        self._model = model
        self._base_url = base_url.rstrip('/')
        self._temperature = temperature
        self._max_tokens = max_tokens
        self._top_p = top_p
        self._frequency_penalty = frequency_penalty
        self._presence_penalty = presence_penalty
        self._timeout = timeout
        self._system_prompt = system_prompt
        self._clean_response = clean_response
        
        # Use OpenAI-compatible client for Ollama
        self._openai_llm = OpenAILLM.with_ollama(
            model=model,
            base_url=base_url,
            temperature=temperature,
        )
        
        # Test connection
        asyncio.create_task(self._test_connection())
    
    async def _test_connection(self):
        """Test connection to Ollama server"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self._base_url.replace('/v1', '')}/api/tags",
                    timeout=5.0
                )
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    model_names = [m.get("name", "") for m in models]
                    logger.info(f"Connected to Ollama. Available models: {model_names}")
                    
                    # Check if our model is available
                    if not any(self._model in name for name in model_names):
                        logger.warning(f"Model '{self._model}' not found in available models")
                else:
                    logger.warning(f"Ollama connection test failed: {response.status_code}")
        except Exception as e:
            logger.error(f"Failed to connect to Ollama: {e}")
    
    async def chat(
        self,
        *,
        chat_ctx: llm.ChatContext,
        conn_options: llm.APIConnectOptions = llm.DEFAULT_API_CONNECT_OPTIONS,
        fnc_ctx: Optional[llm.FunctionContext] = None,
    ) -> "OllamaLLMStream":
        """Start a chat completion"""
        return OllamaLLMStream(
            llm=self,
            openai_llm=self._openai_llm,
            chat_ctx=chat_ctx,
            conn_options=conn_options,
            fnc_ctx=fnc_ctx,
        )
    
    def _clean_model_response(self, text: str) -> str:
        """Clean model response by removing thinking tags and other artifacts"""
        if not self._clean_response:
            return text
        
        # Remove <think> tags and content
        text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
        
        # Remove other common artifacts
        text = re.sub(r'<thinking>.*?</thinking>', '', text, flags=re.DOTALL)
        text = re.sub(r'\*thinks\*.*?\*thinks\*', '', text, flags=re.DOTALL)
        text = re.sub(r'\[thinking\].*?\[/thinking\]', '', text, flags=re.DOTALL)
        
        # Clean up extra whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = text.strip()
        
        return text


class OllamaLLMStream:
    """Stream for Ollama LLM responses"""
    
    def __init__(
        self,
        *,
        llm: OllamaLLM,
        openai_llm: OpenAILLM,
        chat_ctx: llm.ChatContext,
        conn_options: llm.APIConnectOptions,
        fnc_ctx: Optional[llm.FunctionContext],
    ):
        self._llm = llm
        self._openai_llm = openai_llm
        self._chat_ctx = chat_ctx
        self._conn_options = conn_options
        self._fnc_ctx = fnc_ctx
    
    async def __aiter__(self):
        """Stream chat completion responses"""
        try:
            # Use the OpenAI LLM for actual communication
            openai_stream = await self._openai_llm.chat(
                chat_ctx=self._chat_ctx,
                conn_options=self._conn_options,
                fnc_ctx=self._fnc_ctx,
            )
            
            accumulated_text = ""
            
            async for chunk in openai_stream:
                if isinstance(chunk, llm.ChatChunk):
                    # Accumulate text for cleaning
                    if chunk.choices and chunk.choices[0].delta.content:
                        accumulated_text += chunk.choices[0].delta.content
                    
                    yield chunk
                    
                elif isinstance(chunk, llm.ChatResponse):
                    # Clean the final response
                    if chunk.choices and chunk.choices[0].message.content:
                        original_content = chunk.choices[0].message.content
                        cleaned_content = self._llm._clean_model_response(original_content)
                        
                        # Update the response with cleaned content
                        chunk.choices[0].message.content = cleaned_content
                    
                    yield chunk
                else:
                    yield chunk
                    
        except Exception as e:
            logger.error(f"Ollama LLM stream failed: {e}")
            raise


class QwenVisionLLM(OllamaLLM):
    """Specialized Qwen 2.5-VL model for vision tasks"""
    
    def __init__(
        self,
        *,
        model: str = "qwen2.5vl:32b",
        base_url: str = "http://localhost:11434/v1",
        **kwargs
    ):
        """Initialize Qwen Vision LLM"""
        super().__init__(
            model=model,
            base_url=base_url,
            system_prompt="You are a helpful AI assistant with vision capabilities. "
                         "You can see and analyze images, and provide detailed descriptions "
                         "and answers about visual content.",
            **kwargs
        )
        
        self._vision_enabled = True
    
    async def analyze_image(
        self,
        image_data: bytes,
        prompt: str = "Describe what you see in this image.",
        *,
        chat_ctx: Optional[llm.ChatContext] = None,
    ) -> str:
        """Analyze an image using Qwen 2.5-VL"""
        try:
            # Create chat context if not provided
            if chat_ctx is None:
                chat_ctx = llm.ChatContext()
            
            # Add image message to context
            # Note: This is a simplified implementation
            # In practice, you'd need to handle image encoding properly
            image_message = llm.ChatMessage.create(
                text=prompt,
                role="user",
                # images=[image_data]  # This would need proper implementation
            )
            
            chat_ctx.messages.append(image_message)
            
            # Get response
            stream = await self.chat(chat_ctx=chat_ctx)
            response_text = ""
            
            async for chunk in stream:
                if isinstance(chunk, llm.ChatResponse):
                    if chunk.choices and chunk.choices[0].message.content:
                        response_text = chunk.choices[0].message.content
                        break
            
            return self._clean_model_response(response_text)
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            return "I'm sorry, I couldn't analyze the image."


class OllamaModelManager:
    """Manager for Ollama models and operations"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self._base_url = base_url.rstrip('/')
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """List available Ollama models"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self._base_url}/api/tags")
                if response.status_code == 200:
                    return response.json().get("models", [])
                else:
                    logger.error(f"Failed to list models: {response.status_code}")
                    return []
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []
    
    async def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry"""
        try:
            async with httpx.AsyncClient(timeout=300.0) as client:
                response = await client.post(
                    f"{self._base_url}/api/pull",
                    json={"name": model_name}
                )
                return response.status_code == 200
        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {e}")
            return False
    
    async def check_model_availability(self, model_name: str) -> bool:
        """Check if a model is available locally"""
        models = await self.list_models()
        return any(model_name in model.get("name", "") for model in models)
    
    async def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific model"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self._base_url}/api/show",
                    json={"name": model_name}
                )
                if response.status_code == 200:
                    return response.json()
                else:
                    return None
        except Exception as e:
            logger.error(f"Error getting model info for {model_name}: {e}")
            return None
