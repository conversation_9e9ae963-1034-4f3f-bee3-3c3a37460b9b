# 🚀 AI Voice Agent - Clean & Fast

**Ultra-fast voice conversation system with <PERSON>wen 2.5VL**

## ✨ Features

- 🧠 **Qwen 2.5VL Integration** - Uses your local Ollama model
- 🗣️ **Voice Output** - AI speaks responses using TTS
- ⌨️ **Text Input** - Type messages for conversation
- ⚡ **Instant Startup** - No model downloads, ready in <1 second
- 🚀 **Optimized for Speed** - Fast responses and minimal dependencies

## 🎯 Quick Start

### Prerequisites
- Ollama running with Qwen 2.5VL model
- Python 3.8+
- Basic dependencies (auto-installed)

### Installation & Run
```bash
# Install dependencies (if needed)
pip install pyttsx3 httpx

# Run the voice system
python SIMPLE_VOICE_SYSTEM.py
```

### Usage
1. **Start the system** - Run the Python file
2. **Type your message** - Enter text when prompted
3. **Listen to AI response** - <PERSON> will speak back to you
4. **Continue conversation** - Keep typing for ongoing chat
5. **Exit** - Type 'quit', 'exit', or 'bye' to stop

## 🛠️ System Requirements

- **Ollama** - Must be running (`ollama serve`)
- **<PERSON>wen Model** - Any Qwen model (preferably 2.5VL)
- **Python Packages** - pyttsx3, httpx (minimal dependencies)

## 🔧 Troubleshooting

**Ollama Connection Issues:**
```bash
# Start Ollama
ollama serve

# Install Qwen model (if needed)
ollama pull qwen2.5-vl
```

**TTS Issues:**
- Windows: Uses built-in SAPI voices
- Automatically falls back to text output if TTS fails

## 📊 Performance

- **Startup Time**: <1 second
- **Response Time**: 1-3 seconds (depends on model size)
- **Memory Usage**: Minimal (no heavy models loaded)
- **Dependencies**: Only 2 packages required

## 🧹 Clean Architecture

This is a completely cleaned codebase with:
- ✅ Single working file
- ✅ Minimal dependencies
- ✅ No junk or legacy code
- ✅ Fast and reliable
- ✅ Well-documented

## 🎭 Conversation Features

- **Natural Responses** - Conversational and friendly
- **Context Memory** - Remembers recent conversation
- **Emotion Detection** - Responds appropriately to tone
- **Performance Tracking** - Shows response time statistics

---

**Ready to chat with your AI assistant!** 🤖💬
