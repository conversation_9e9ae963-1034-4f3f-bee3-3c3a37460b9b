"""
Install Pure Local Voice System Dependencies
100% Local processing - NO external APIs
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def install_package(package_name, description=""):
    """Install a package with error handling"""
    try:
        logger.info(f"📦 Installing {package_name} - {description}")
        subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                      check=True, capture_output=True)
        logger.info(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {package_name}: {e}")
        return False


def main():
    """Install all pure local voice dependencies"""
    logger.info("🚀 Installing PURE LOCAL voice system dependencies...")
    
    # Core packages for 100% local processing
    packages = [
        # Local AI model
        ("transformers", "Hugging Face transformers for DialoGPT"),
        ("torch", "PyTorch for AI models"),
        ("accelerate", "Model acceleration"),
        
        # Premium TTS (ChatTTS)
        ("ChatTTS", "Premium conversational TTS"),
        
        # Advanced VAD (Silero)
        ("torch", "PyTorch for Silero VAD"),
        
        # Speech recognition (Whisper)
        ("openai-whisper", "Local speech recognition"),
        
        # Audio processing
        ("soundfile", "Audio file handling"),
        ("librosa", "Audio analysis"),
        ("sounddevice", "Real-time audio"),
        ("pygame", "Audio playback"),
        ("numpy", "Audio processing"),
        ("scipy", "Signal processing"),
        
        # Fallback TTS
        ("pyttsx3", "Cross-platform TTS fallback"),
        
        # Additional utilities
        ("asyncio", "Async programming"),
        ("pathlib", "Path handling"),
    ]
    
    successful = 0
    failed = 0
    
    for package, description in packages:
        if install_package(package, description):
            successful += 1
        else:
            failed += 1
    
    logger.info(f"\n📊 Installation Summary:")
    logger.info(f"✅ Successful: {successful}")
    logger.info(f"❌ Failed: {failed}")
    
    if failed > 0:
        logger.warning("⚠️ Some packages failed to install. The system will use fallback options.")
    
    logger.info("🎉 Pure local voice system dependencies installation complete!")
    
    # Test installations
    logger.info("\n🧪 Testing installations...")
    
    # Test transformers
    try:
        from transformers import AutoTokenizer
        logger.info("✅ Transformers working")
    except:
        logger.error("❌ Transformers failed")
    
    # Test torch
    try:
        import torch
        logger.info("✅ PyTorch working")
    except:
        logger.error("❌ PyTorch failed")
    
    # Test ChatTTS
    try:
        import ChatTTS
        logger.info("✅ ChatTTS available")
    except:
        logger.warning("⚠️ ChatTTS not available - will use fallback TTS")
    
    # Test Whisper
    try:
        import whisper
        logger.info("✅ Whisper working")
    except:
        logger.error("❌ Whisper failed")
    
    # Test audio libraries
    try:
        import sounddevice
        import soundfile
        import pygame
        logger.info("✅ Audio libraries working")
    except:
        logger.error("❌ Audio libraries failed")
    
    # Additional instructions
    print("\n" + "="*60)
    print("🚀 PURE LOCAL VOICE SYSTEM READY!")
    print("="*60)
    print("✅ 100% Local processing - NO external APIs")
    print("🧠 DialoGPT: Local conversational AI (345MB)")
    print("🗣️ ChatTTS: Premium voice quality")
    print("🎤 Silero VAD: Advanced voice detection")
    print("⚡ Ultra-low latency responses")
    print()
    print("To start the pure local conversation:")
    print("  python local_ai_voice_agent/examples/pure_local_voice_chat.py")
    print()
    print("Features:")
    print("  🏠 100% local processing")
    print("  🚫 NO external API calls")
    print("  ⚡ Ultra-fast responses")
    print("  🗣️ Premium voice quality")
    print("  🎭 Emotional expression")
    print("  💬 Natural conversation flow")
    print("="*60)


if __name__ == "__main__":
    main()
