"""
Test script for Ultimate TTS System
Tests F5-TTS, ChatTTS, and Silero VAD functionality
"""

import asyncio
import logging
import sys
import tempfile
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_f5_tts():
    """Test F5-TTS functionality"""
    logger.info("🧪 Testing F5-TTS...")
    
    try:
        from f5_tts.api import F5TTS
        
        # Initialize F5-TTS
        f5_tts = F5TTS(
            model_type="F5-TTS",
            ckpt_file=None,
            vocab_file=None,
            ode_method="euler",
            use_ema=True,
            device="auto"
        )
        
        logger.info("✅ F5-TTS initialized successfully")
        
        # Test text
        test_text = "Hello! This is a test of the F5-TTS system with ultra-high voice quality."
        
        logger.info(f"🗣️ Generating speech: '{test_text}'")
        
        # Note: F5-TTS typically requires reference audio for voice cloning
        # For testing, we'll try basic generation
        try:
            audio_data = f5_tts.infer(
                text=test_text,
                ref_audio=None,
                ref_text="",
                target_sample_rate=24000,
                cross_fade_duration=0.15
            )
            
            logger.info("✅ F5-TTS speech generation successful")
            logger.info(f"📊 Audio shape: {audio_data.shape if hasattr(audio_data, 'shape') else 'Unknown'}")
            
            return True
            
        except Exception as e:
            logger.warning(f"F5-TTS generation failed (expected without reference audio): {e}")
            return False
            
    except ImportError as e:
        logger.error(f"❌ F5-TTS import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ F5-TTS test failed: {e}")
        return False


async def test_chattts():
    """Test ChatTTS functionality"""
    logger.info("🧪 Testing ChatTTS...")
    
    try:
        import ChatTTS
        import torch
        import soundfile as sf
        
        # Initialize ChatTTS
        chat = ChatTTS.Chat()
        chat.load(compile=False)
        
        logger.info("✅ ChatTTS initialized successfully")
        
        # Test text
        test_text = "Hello! This is a test of the ChatTTS conversational voice system."
        
        logger.info(f"🗣️ Generating speech: '{test_text}'")
        
        # Generate speech
        inputs = chat.infer([test_text])
        
        if inputs and len(inputs) > 0:
            logger.info("✅ ChatTTS speech generation successful")
            logger.info(f"📊 Audio shape: {inputs[0].shape}")
            
            # Save test audio
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
                sf.write(tmp_file.name, inputs[0], 24000)
                logger.info(f"💾 Test audio saved: {tmp_file.name}")
                
                # Clean up
                os.unlink(tmp_file.name)
            
            return True
        else:
            logger.error("❌ ChatTTS generated empty audio")
            return False
            
    except ImportError as e:
        logger.error(f"❌ ChatTTS import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ ChatTTS test failed: {e}")
        return False


async def test_silero_vad():
    """Test Silero VAD functionality"""
    logger.info("🧪 Testing Silero VAD...")
    
    try:
        import torch
        import numpy as np
        
        # Load Silero VAD
        model, utils = torch.hub.load(
            repo_or_dir='snakers4/silero-vad',
            model='silero_vad',
            force_reload=False,
            onnx=False
        )
        
        logger.info("✅ Silero VAD loaded successfully")
        
        # Create test audio (simulated)
        sample_rate = 16000
        duration = 2  # seconds
        test_audio = torch.randn(sample_rate * duration)
        
        logger.info(f"🎵 Testing with {duration}s audio sample")
        
        # Test VAD
        get_speech_timestamps, save_audio, read_audio, VADIterator, collect_chunks = utils
        
        speech_timestamps = get_speech_timestamps(
            test_audio, 
            model,
            sampling_rate=sample_rate
        )
        
        logger.info(f"✅ Silero VAD processing successful")
        logger.info(f"📊 Speech segments detected: {len(speech_timestamps)}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Silero VAD import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Silero VAD test failed: {e}")
        return False


async def test_audio_libraries():
    """Test audio processing libraries"""
    logger.info("🧪 Testing audio processing libraries...")
    
    try:
        import librosa
        import soundfile as sf
        import numpy as np
        import torch
        import torchaudio
        
        logger.info("✅ All audio libraries imported successfully")
        
        # Test basic audio processing
        sample_rate = 22050
        duration = 1
        test_audio = np.sin(2 * np.pi * 440 * np.linspace(0, duration, sample_rate))
        
        # Test soundfile
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            sf.write(tmp_file.name, test_audio, sample_rate)
            loaded_audio, loaded_sr = sf.read(tmp_file.name)
            
            logger.info(f"✅ Audio I/O test successful: {loaded_audio.shape}, {loaded_sr}Hz")
            os.unlink(tmp_file.name)
        
        # Test torch audio
        tensor_audio = torch.from_numpy(test_audio).float()
        logger.info(f"✅ Torch audio conversion successful: {tensor_audio.shape}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Audio libraries import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Audio libraries test failed: {e}")
        return False


async def main():
    """Run all tests"""
    logger.info("🎭 ULTIMATE TTS SYSTEM TEST SUITE")
    logger.info("=" * 60)
    
    tests = [
        ("Audio Libraries", test_audio_libraries),
        ("Silero VAD", test_silero_vad),
        ("ChatTTS", test_chattts),
        ("F5-TTS", test_f5_tts),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("🎯 TEST RESULTS SUMMARY")
    logger.info("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    logger.info(f"\n📊 OVERALL RESULTS:")
    logger.info(f"   ✅ Passed: {passed}/{total}")
    logger.info(f"   📈 Success Rate: {passed/total*100:.1f}%")
    
    if passed >= total * 0.75:  # 75% success rate
        logger.info("\n🎉 ULTIMATE TTS SYSTEM IS READY!")
        logger.info("🚀 You can now run ultimate_natural_voice_chat.py")
    else:
        logger.warning("\n⚠️ Some components failed. System may have limited functionality.")
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
