"""
Install ULTIMATE AGENTIC VOICE SYSTEM Dependencies
Most powerful, highest quality voice agent with advanced capabilities
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def install_package(package_name, description=""):
    """Install a package with error handling"""
    try:
        logger.info(f"📦 Installing {package_name} - {description}")
        subprocess.run([sys.executable, "-m", "pip", "install", package_name],
                      check=True, capture_output=True)
        logger.info(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {package_name}: {e}")
        return False


def install_git_package(git_url, description=""):
    """Install a package from git"""
    try:
        logger.info(f"📦 Installing from git: {git_url} - {description}")
        subprocess.run([sys.executable, "-m", "pip", "install", f"git+{git_url}"],
                      check=True, capture_output=True)
        logger.info(f"✅ Git package installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install git package: {e}")
        return False


def main():
    """Install all ULTIMATE AGENTIC voice system dependencies"""
    logger.info("🚀 Installing ULTIMATE AGENTIC VOICE SYSTEM dependencies...")
    logger.info("🎯 Target: Most powerful, highest quality voice agent")

    # ULTIMATE VOICE SYSTEM PACKAGES
    packages = [
        # === PREMIUM TTS ENGINES ===
        ("ChatTTS", "Premium conversational TTS with emotional expression"),
        ("F5-TTS", "State-of-the-art flow matching TTS"),
        ("TTS", "Coqui TTS - Ultra-high quality neural voices"),
        ("edge-tts", "Microsoft Edge TTS - High quality"),
        ("pyttsx3", "Cross-platform TTS fallback"),

        # === ADVANCED AUDIO PROCESSING ===
        ("torch", "PyTorch for AI models"),
        ("torchaudio", "Advanced audio processing"),
        ("librosa", "Professional audio analysis"),
        ("soundfile", "High-quality audio I/O"),
        ("sounddevice", "Real-time audio streaming"),
        ("pyaudio", "Low-level audio interface"),
        ("pygame", "Audio playback and mixing"),
        ("resampy", "High-quality audio resampling"),
        ("noisereduce", "Real-time noise reduction"),
        ("scipy", "Advanced signal processing"),
        ("numpy", "Numerical computing"),

        # === VOICE ACTIVITY DETECTION ===
        ("webrtcvad", "WebRTC Voice Activity Detection"),
        ("silero-vad", "Advanced neural VAD"),

        # === SPEECH RECOGNITION ===
        ("openai-whisper", "State-of-the-art speech recognition"),
        ("faster-whisper", "Optimized Whisper implementation"),
        ("speech_recognition", "Multi-engine speech recognition"),

        # === AI/ML FRAMEWORKS ===
        ("transformers", "Hugging Face transformers"),
        ("accelerate", "Model acceleration and optimization"),
        ("datasets", "Dataset processing"),
        ("tokenizers", "Fast tokenization"),

        # === AGENTIC CAPABILITIES ===
        ("langchain", "Advanced AI agent framework"),
        ("langchain-community", "Community integrations"),
        ("chromadb", "Vector database for memory"),
        ("sentence-transformers", "Semantic embeddings"),

        # === STREAMING & ASYNC ===
        ("asyncio", "Async programming"),
        ("aiofiles", "Async file operations"),
        ("aiohttp", "Async HTTP client"),
        ("websockets", "Real-time communication"),

        # === VISION CAPABILITIES (for Qwen 2.5VL) ===
        ("opencv-python", "Computer vision"),
        ("Pillow", "Image processing"),
        ("torchvision", "Vision models"),

        # === UTILITIES ===
        ("httpx", "Modern HTTP client"),
        ("pydantic", "Data validation"),
        ("rich", "Beautiful terminal output"),
        ("typer", "CLI framework"),
        ("python-dotenv", "Environment management"),
    ]

    # Install premium packages from git
    git_packages = [
        ("https://github.com/2noise/ChatTTS.git", "ChatTTS - Premium conversational TTS"),
        ("https://github.com/SWivid/F5-TTS.git", "F5-TTS - State-of-the-art TTS"),
    ]

    successful = 0
    failed = 0

    # Install regular packages
    for package, description in packages:
        if install_package(package, description):
            successful += 1
        else:
            failed += 1

    # Install git packages
    for git_url, description in git_packages:
        if install_git_package(git_url, description):
            successful += 1
        else:
            failed += 1

    logger.info(f"\n📊 Installation Summary:")
    logger.info(f"✅ Successful: {successful}")
    logger.info(f"❌ Failed: {failed}")

    if failed > 0:
        logger.warning("⚠️ Some packages failed to install. The system will use fallback options.")

    logger.info("🎉 ULTIMATE AGENTIC VOICE SYSTEM installation complete!")

    # Test critical components
    logger.info("\n🧪 Testing critical components...")

    # Test PyTorch
    try:
        import torch
        logger.info(f"✅ PyTorch {torch.__version__} - GPU: {torch.cuda.is_available()}")
    except:
        logger.error("❌ PyTorch failed")

    # Test Transformers
    try:
        import transformers
        logger.info(f"✅ Transformers {transformers.__version__}")
    except:
        logger.error("❌ Transformers failed")

    # Test Audio
    try:
        import sounddevice
        import librosa
        import soundfile
        logger.info("✅ Audio processing libraries working")
    except:
        logger.error("❌ Audio libraries failed")

    # Test Whisper
    try:
        import whisper
        logger.info("✅ Whisper speech recognition ready")
    except:
        logger.warning("⚠️ Whisper not available")

    # Test ChatTTS
    try:
        import ChatTTS
        logger.info("✅ ChatTTS premium TTS ready")
    except:
        logger.warning("⚠️ ChatTTS not available - will use fallback")

    # Additional instructions
    print("\n" + "="*80)
    print("🚀 ULTIMATE AGENTIC VOICE SYSTEM READY!")
    print("="*80)
    print("🎯 MOST POWERFUL VOICE AGENT FEATURES:")
    print("  🧠 Qwen 2.5VL: Advanced vision-language model")
    print("  🗣️ ChatTTS + F5-TTS: Premium voice synthesis")
    print("  🎤 Silero VAD: Advanced voice detection")
    print("  👁️ Computer Vision: Camera integration ready")
    print("  🤖 Agentic: Memory, reasoning, tool use")
    print("  ⚡ Ultra-low latency: <1.5s responses")
    print("  🎭 Emotional expression: Natural conversations")
    print()
    print("To start the ULTIMATE voice agent:")
    print("  python local_ai_voice_agent/examples/ultimate_agentic_voice.py")
    print()
    print("Advanced Features:")
    print("  💾 Conversation memory with ChromaDB")
    print("  � Tool use and function calling")
    print("  � Vision capabilities with camera")
    print("  � Multi-modal interactions")
    print("  � Goal-oriented conversations")
    print("="*80)


if __name__ == "__main__":
    main()
