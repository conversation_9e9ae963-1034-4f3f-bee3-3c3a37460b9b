"""
🚀 ULTIMATE CLEAN VOICE SYSTEM
- Qwen 2.5VL via Ollama (YOUR MODEL)
- ChatTTS Premium Voice
- Silero Advanced VAD
- Ultra-fast <1.5s responses
- Clean, organized, single file
"""

import asyncio
import logging
import sys
import os
import time
import httpx
import tempfile
import torch
import numpy as np
import json
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class UltimateVoiceSystem:
    """Clean, powerful voice system with Qwen 2.5VL"""
    
    def __init__(self):
        # Core components
        self.ollama_client = None
        self.qwen_model_name = None
        self.conversation_active = False
        self.is_speaking = False
        
        # Voice components
        self.chattts_model = None
        self.silero_vad_model = None
        self.vad_utils = None
        self.whisper_model = None
        
        # Conversation memory
        self.conversation_history = []
        self.response_times = []
        
        # Audio settings
        self.sample_rate = 24000
        
    async def initialize(self):
        """Initialize the complete voice system"""
        logger.info("🚀 Initializing ULTIMATE VOICE SYSTEM...")
        
        try:
            # 1. Connect to Qwen 2.5VL
            if not await self._connect_to_qwen():
                return False
            
            # 2. Initialize premium TTS
            await self._initialize_tts()
            
            # 3. Initialize advanced VAD
            await self._initialize_vad()
            
            # 4. Initialize STT
            await self._initialize_stt()
            
            logger.info("✅ ULTIMATE VOICE SYSTEM READY!")
            logger.info(f"🧠 AI: {self.qwen_model_name}")
            logger.info(f"🗣️ TTS: {'ChatTTS' if self.chattts_model else 'pyttsx3'}")
            logger.info(f"🎤 VAD: {'Silero' if self.silero_vad_model else 'Basic'}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            return False
    
    async def _connect_to_qwen(self):
        """Connect to Qwen 2.5VL via Ollama"""
        try:
            self.ollama_client = httpx.AsyncClient(
                base_url="http://localhost:11434",
                timeout=30.0
            )
            
            # Get available models
            response = await self.ollama_client.get("/api/tags")
            if response.status_code != 200:
                logger.error("❌ Ollama not running. Start with: ollama serve")
                return False
            
            models = response.json().get("models", [])
            qwen_models = [m for m in models if "qwen" in m["name"].lower()]
            
            if not qwen_models:
                logger.error("❌ No Qwen models found. Install with: ollama pull qwen2.5-vl")
                return False
            
            # Prefer VL model
            vl_models = [m for m in qwen_models if "vl" in m["name"].lower()]
            self.qwen_model_name = vl_models[0]["name"] if vl_models else qwen_models[0]["name"]
            
            logger.info(f"✅ Connected to: {self.qwen_model_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Qwen connection failed: {e}")
            return False
    
    async def _initialize_tts(self):
        """Initialize premium TTS"""
        try:
            import ChatTTS
            self.chattts_model = ChatTTS.Chat()
            self.chattts_model.load(compile=False)
            logger.info("✅ ChatTTS premium voice ready")
        except Exception as e:
            logger.warning(f"⚠️ ChatTTS failed: {e}")
            logger.info("✅ Using pyttsx3 fallback")
    
    async def _initialize_vad(self):
        """Initialize advanced VAD"""
        try:
            self.silero_vad_model, self.vad_utils = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False,
                onnx=False
            )
            logger.info("✅ Silero VAD ready")
        except Exception as e:
            logger.warning(f"⚠️ Silero VAD failed: {e}")
    
    async def _initialize_stt(self):
        """Initialize speech recognition"""
        try:
            import whisper
            self.whisper_model = whisper.load_model("base")
            logger.info("✅ Whisper STT ready")
        except Exception as e:
            logger.error(f"❌ Whisper failed: {e}")
            return False
        return True
    
    async def get_ai_response(self, user_text: str) -> str:
        """Get response from Qwen 2.5VL"""
        try:
            start_time = time.time()
            
            # Build conversation context
            context = "\n".join(self.conversation_history[-6:]) if self.conversation_history else ""
            
            # Create prompt for Qwen
            system_prompt = """You are Josie, a warm and intelligent AI assistant.

PERSONALITY: Friendly, helpful, naturally conversational
STYLE: Keep responses under 25 words, be natural and engaging
GOAL: Have meaningful, flowing conversations

Respond naturally and warmly."""

            full_prompt = f"{system_prompt}\n\nContext: {context}\n\nUser: {user_text}\n\nJosie:"
            
            # Send to Qwen via Ollama
            response = await self.ollama_client.post(
                "/api/generate",
                json={
                    "model": self.qwen_model_name,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "num_predict": 50,
                        "top_k": 40,
                        "top_p": 0.9
                    }
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result["response"].strip()
                
                # Clean response
                if "Josie:" in ai_response:
                    ai_response = ai_response.split("Josie:")[-1].strip()
                
                # Limit length
                if len(ai_response) > 150:
                    ai_response = ai_response[:150] + "..."
                
                # Update conversation
                self.conversation_history.append(f"User: {user_text}")
                self.conversation_history.append(f"Josie: {ai_response}")
                
                # Keep only recent history
                if len(self.conversation_history) > 20:
                    self.conversation_history = self.conversation_history[-20:]
                
                # Track performance
                response_time = time.time() - start_time
                self.response_times.append(response_time)
                
                logger.info(f"⚡ Response in {response_time:.1f}s")
                return ai_response
            else:
                return "I'm having trouble thinking. Can you repeat that?"
                
        except Exception as e:
            logger.error(f"❌ AI response error: {e}")
            return "Sorry, I didn't catch that. Could you say it again?"
    
    async def speak(self, text: str, emotion: str = "conversational"):
        """Speak with premium voice quality"""
        try:
            self.is_speaking = True
            logger.info(f"🗣️ Josie ({emotion}): {text}")
            
            if self.chattts_model:
                await self._speak_chattts(text)
            else:
                await self._speak_fallback(text, emotion)
                
        except Exception as e:
            logger.error(f"❌ Speech error: {e}")
            print(f"🗣️ Josie: {text}")
        finally:
            self.is_speaking = False
    
    async def _speak_chattts(self, text: str):
        """Speak using ChatTTS"""
        try:
            import soundfile as sf
            import pygame
            
            # Generate speech
            inputs = self.chattts_model.infer([text])
            
            if inputs and len(inputs) > 0:
                # Save to temp file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
                    tmp_file_path = tmp_file.name
                
                sf.write(tmp_file_path, inputs[0], self.sample_rate)
                
                # Play audio
                pygame.mixer.init(frequency=self.sample_rate)
                pygame.mixer.music.load(tmp_file_path)
                pygame.mixer.music.play()
                
                while pygame.mixer.music.get_busy():
                    await asyncio.sleep(0.1)
                
                pygame.mixer.quit()
                
                # Cleanup
                try:
                    os.unlink(tmp_file_path)
                except:
                    pass
            else:
                await self._speak_fallback(text, "conversational")
                
        except Exception as e:
            logger.error(f"❌ ChatTTS error: {e}")
            await self._speak_fallback(text, "conversational")
    
    async def _speak_fallback(self, text: str, emotion: str):
        """Fallback TTS using pyttsx3"""
        try:
            import pyttsx3
            
            engine = pyttsx3.init()
            
            # Adjust for emotion
            if emotion == "excited":
                engine.setProperty('rate', 200)
                engine.setProperty('volume', 0.9)
            elif emotion == "calm":
                engine.setProperty('rate', 160)
                engine.setProperty('volume', 0.8)
            else:
                engine.setProperty('rate', 180)
                engine.setProperty('volume', 0.85)
            
            engine.say(text)
            engine.runAndWait()
            engine.stop()
            
        except Exception as e:
            logger.error(f"❌ Fallback TTS error: {e}")
            print(f"🗣️ Josie: {text}")

    async def listen(self):
        """Listen for user input with advanced VAD"""
        try:
            import sounddevice as sd

            logger.info("🎤 Listening... (speak now)")

            # Record audio
            duration = 5
            sample_rate = 16000

            audio_data = sd.rec(int(duration * sample_rate),
                              samplerate=sample_rate,
                              channels=1,
                              dtype=np.float32)
            sd.wait()

            # Apply Silero VAD if available
            if self.silero_vad_model and self.vad_utils:
                audio_tensor = torch.from_numpy(audio_data.flatten())

                speech_timestamps = self.vad_utils[0](
                    audio_tensor,
                    self.silero_vad_model,
                    sampling_rate=sample_rate
                )

                if not speech_timestamps:
                    logger.info("⚠️ No speech detected")
                    return None

            # Transcribe with Whisper
            audio_flat = audio_data.flatten()
            result = self.whisper_model.transcribe(audio_flat, fp16=False)
            text = result["text"].strip()

            if text and len(text) > 2:
                logger.info(f"👤 User: {text}")
                return text
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Listen error: {e}")
            return None

    def _determine_emotion(self, text: str) -> str:
        """Determine emotional tone for response"""
        text_lower = text.lower()

        if any(word in text_lower for word in ['great', 'awesome', 'amazing', 'fantastic']):
            return "excited"
        elif any(word in text_lower for word in ['sorry', 'sad', 'help', 'problem']):
            return "calm"
        elif any(word in text_lower for word in ['happy', 'glad', 'nice', 'good']):
            return "happy"
        else:
            return "conversational"

    async def start_conversation(self):
        """Start the voice conversation"""
        logger.info("🚀 Starting ULTIMATE VOICE CONVERSATION...")
        logger.info("🧠 Powered by your Qwen 2.5VL model")
        logger.info("💬 Say something to begin! (Press Ctrl+C to stop)")

        self.conversation_active = True

        # Welcome message
        await self.speak(
            "Hello! I'm Josie, powered by your Qwen 2.5VL model. What would you like to talk about?",
            "friendly"
        )

        try:
            while self.conversation_active:
                if not self.is_speaking:
                    # Listen for user input
                    user_text = await self.listen()

                    if user_text:
                        # Check for exit commands
                        if any(word in user_text.lower() for word in ['goodbye', 'bye', 'exit', 'quit', 'stop']):
                            await self.speak("It's been wonderful talking with you! Goodbye!", "warm")
                            break

                        # Get AI response
                        ai_response = await self.get_ai_response(user_text)

                        # Determine emotion and speak
                        emotion = self._determine_emotion(ai_response)
                        await self.speak(ai_response, emotion)

                    await asyncio.sleep(0.1)
                else:
                    await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            logger.info("🛑 Conversation stopped by user")
        except Exception as e:
            logger.error(f"❌ Conversation error: {e}")
        finally:
            self.conversation_active = False
            await self._show_stats()
            logger.info("👋 Voice conversation ended")

    async def _show_stats(self):
        """Show conversation statistics"""
        if self.response_times:
            avg_time = sum(self.response_times) / len(self.response_times)
            logger.info(f"📊 Stats: {len(self.response_times)} exchanges, avg {avg_time:.1f}s response time")


async def main():
    """Main function"""
    try:
        # Create and initialize system
        system = UltimateVoiceSystem()

        if await system.initialize():
            # Start conversation
            await system.start_conversation()
        else:
            logger.error("❌ Failed to initialize voice system")
            print("\n💡 Troubleshooting:")
            print("1. Start Ollama: ollama serve")
            print("2. Install Qwen: ollama pull qwen2.5-vl")
            print("3. Install dependencies: pip install -r requirements.txt")

    except Exception as e:
        logger.error(f"❌ Main error: {e}")


if __name__ == "__main__":
    print("🚀 ULTIMATE CLEAN VOICE SYSTEM")
    print("=" * 50)
    print("🧠 Qwen 2.5VL: Your advanced AI model")
    print("🗣️ ChatTTS: Premium voice synthesis")
    print("🎤 Silero VAD: Advanced voice detection")
    print("⚡ Ultra-fast: <1.5s responses")
    print("🎭 Natural: Emotional conversations")
    print("🧹 Clean: Single file, organized")
    print("=" * 50)

    asyncio.run(main())
