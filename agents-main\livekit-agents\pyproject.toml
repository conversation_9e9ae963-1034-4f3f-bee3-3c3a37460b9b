[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "livekit-agents"
dynamic = ["version"]
description = "A powerful framework for building realtime voice AI agents"
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.9"
authors = [{ name = "LiveKit", email = "<EMAIL>" }]
keywords = ["webrtc", "realtime", "audio", "video", "livekit", "agents", "AI"]
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Multimedia :: Video",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3 :: Only",
]
dependencies = [
    "click~=8.1",
    "livekit>=1.0.9,<2",
    "livekit-api>=1.0.2,<2",
    "livekit-protocol~=1.0",
    "protobuf>=3",
    "pyjwt>=2.0",
    "types-protobuf>=4,<5",
    "watchfiles>=1.0",
    "psutil>=7.0",
    "aiohttp~=3.10",
    "typing-extensions>=4.12",
    "sounddevice>=0.5",
    "docstring_parser>=0.16",
    "eval-type-backport",
    "colorama>=0.4.6",
    "av>=12.0.0",
    "numpy>=1.26.0",
    "pydantic>=2.0,<3",
    "nest-asyncio>=1.6.0",
]

[project.optional-dependencies]
mcp = ["mcp>=1.6.0, <2; python_version >= '3.10'"]
codecs = ["av>=12.0.0", "numpy>=1.26.0"]
images = ["pillow>=10.3.0"]
aws = ["livekit-plugins-aws>=1.1.4"]
neuphonic = ["livekit-plugins-neuphonic>=1.1.4"]
playai = ["livekit-plugins-playai>=1.1.4"]
turn-detector = ["livekit-plugins-turn-detector>=1.1.4"]
assemblyai = ["livekit-plugins-assemblyai>=1.1.4"]
rime = ["livekit-plugins-rime>=1.1.4"]
nltk = ["livekit-plugins-nltk>=1.1.4"]
anthropic = ["livekit-plugins-anthropic>=1.1.4"]
openai = ["livekit-plugins-openai>=1.1.4"]
groq = ["livekit-plugins-groq>=1.1.4"]
elevenlabs = ["livekit-plugins-elevenlabs>=1.1.4"]
azure = ["livekit-plugins-azure>=1.1.4"]
fal = ["livekit-plugins-fal>=1.1.4"]
clova = ["livekit-plugins-clova>=1.1.4"]
deepgram = ["livekit-plugins-deepgram>=1.1.4"]
silero = ["livekit-plugins-silero>=1.1.4"]
cartesia = ["livekit-plugins-cartesia>=1.1.4"]
speechmatics = ["livekit-plugins-speechmatics>=1.1.4"]
google = ["livekit-plugins-google>=1.1.4"]
gladia = ["livekit-plugins-gladia>=1.1.4"]
resemble = ["livekit-plugins-resemble>=1.1.4"]
bey = ["livekit-plugins-bey>=1.1.4"]
bithuman = ["livekit-plugins-bithuman>=1.1.4"]
speechify = ["livekit-plugins-speechify>=1.1.4"]
tavus = ["livekit-plugins-tavus>=1.1.4"]
hume = ["livekit-plugins-hume>=1.1.4"]
lmnt = ["livekit-plugins-lmnt>=1.1.4"]
baseten = ["livekit-plugins-baseten>=1.1.4"]
langchain = ["livekit-plugins-langchain>=1.1.4"]
sarvam = ["livekit-plugins-sarvam>=1.1.4"]
spitch = ["livekit-plugins-spitch>=1.1.4"]
inworld = ["livekit-plugins-inworld>=1.1.4"]
hedra = ["livekit-plugins-hedra>=1.1.4"]


[project.urls]
Documentation = "https://docs.livekit.io"
Website = "https://livekit.io/"
Source = "https://github.com/livekit/agents"

[tool.hatch.version]
path = "livekit/agents/version.py"

[tool.hatch.build.targets.wheel]
packages = ["livekit"]
include = ["livekit/agents/resources/*", "livekit/agents/debug/index.html"]


[tool.hatch.build.targets.sdist]
include = ["/livekit"]
